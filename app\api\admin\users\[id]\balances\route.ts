import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../../lib/supabase/server'
import { z } from 'zod'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Validation schema for balance operations
const balanceOperationSchema = z.object({
  currency_code: z.string().min(3).max(3),
  amount: z.number(),
  operation: z.enum(['add', 'subtract', 'set']),
  notes: z.string().optional()
})

// GET /api/admin/users/[id]/balances - Get user's currency balances
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 20)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get admin's tenant and verify admin role
    const { data: adminProfile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!adminProfile || adminProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Verify target user exists in same tenant
    const { data: targetUser } = await supabase
      .from('user_profiles')
      .select('id, name')
      .eq('id', params.id)
      .eq('tenant_id', adminProfile.tenant_id) // 🔒 CRITICAL: Same tenant only
      .single()

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get user's currency balances
    const { data: balances, error } = await supabase
      .from('user_currency_balances')
      .select(`
        currency_code,
        balance,
        created_at,
        updated_at,
        currencies (
          name,
          exchange_rate,
          is_active
        )
      `)
      .eq('user_id', params.id)
      .eq('tenant_id', adminProfile.tenant_id)
      .order('currency_code')

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch balances' }, { status: 500 })
    }

    // Get available currencies for this tenant
    const { data: availableCurrencies } = await supabase
      .from('currencies')
      .select('code, name, exchange_rate')
      .eq('tenant_id', adminProfile.tenant_id)
      .eq('is_active', true)
      .order('code')

    return NextResponse.json({
      user: {
        id: targetUser.id,
        name: targetUser.name
      },
      balances: balances?.map(b => ({
        currencyCode: b.currency_code,
        balance: Number(b.balance) || 0,
        currencyName: b.currencies?.name || b.currency_code,
        exchangeRate: Number(b.currencies?.exchange_rate) || 1,
        isActive: b.currencies?.is_active || false,
        updatedAt: b.updated_at
      })) || [],
      availableCurrencies: availableCurrencies || []
    })

  } catch (error) {
    console.error('Error fetching user balances:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/admin/users/[id]/balances - Add/Update user balance
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Request body:', body)

    let currency_code, amount, operation, notes
    try {
      const parsed = balanceOperationSchema.parse(body)
      currency_code = parsed.currency_code
      amount = parsed.amount
      operation = parsed.operation
      notes = parsed.notes
      console.log('Parsed data:', { currency_code, amount, operation, notes })
    } catch (validationError) {
      console.error('Validation error:', validationError)
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationError instanceof Error ? validationError.message : 'Validation failed'
      }, { status: 400 })
    }

    // Get admin's tenant and verify admin role
    console.log('Getting admin profile for user:', user.id)
    const { data: adminProfile, error: adminError } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    console.log('Admin profile:', adminProfile, 'Error:', adminError)

    if (!adminProfile || adminProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Verify target user exists in same tenant
    const { data: targetUser } = await supabase
      .from('user_profiles')
      .select('id, name')
      .eq('id', params.id)
      .eq('tenant_id', adminProfile.tenant_id) // 🔒 CRITICAL: Same tenant only
      .single()

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Verify currency exists and is active in this tenant
    const { data: currency } = await supabase
      .from('currencies')
      .select('code, name')
      .eq('code', currency_code)
      .eq('tenant_id', adminProfile.tenant_id)
      .eq('is_active', true)
      .single()

    if (!currency) {
      return NextResponse.json({ error: 'Invalid or inactive currency' }, { status: 400 })
    }

    // Get current balance
    const { data: currentBalance } = await supabase
      .from('user_currency_balances')
      .select('balance')
      .eq('user_id', params.id)
      .eq('currency_code', currency_code)
      .eq('tenant_id', adminProfile.tenant_id)
      .single()

    const balanceBefore = Number(currentBalance?.balance) || 0
    let balanceAfter: number

    // Calculate new balance based on operation
    switch (operation) {
      case 'add':
        balanceAfter = balanceBefore + amount
        break
      case 'subtract':
        balanceAfter = balanceBefore - amount
        break
      case 'set':
        balanceAfter = amount
        break
      default:
        return NextResponse.json({ error: 'Invalid operation' }, { status: 400 })
    }

    // Prevent negative balances
    if (balanceAfter < 0) {
      return NextResponse.json({ error: 'Balance cannot be negative' }, { status: 400 })
    }

    // Update or insert balance
    console.log('Attempting to upsert balance:', {
      user_id: params.id,
      currency_code,
      balance: balanceAfter,
      tenant_id: adminProfile.tenant_id
    })

    // Try to update first, then insert if it doesn't exist
    const { error: updateError } = await supabase
      .from('user_currency_balances')
      .update({
        balance: balanceAfter,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', params.id)
      .eq('currency_code', currency_code)
      .eq('tenant_id', adminProfile.tenant_id)

    // If update didn't affect any rows, insert a new record
    if (updateError || !currentBalance) {
      console.log('Inserting new balance record')
      const { error: insertError } = await supabase
        .from('user_currency_balances')
        .insert({
          user_id: params.id,
          currency_code,
          balance: balanceAfter,
          tenant_id: adminProfile.tenant_id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (insertError) {
        console.error('Insert error:', insertError)
        return NextResponse.json({
          error: 'Failed to create balance record',
          details: insertError.message
        }, { status: 500 })
      }
    } else {
      console.log('Updated existing balance record')
    }

    // Log the balance change
    console.log('Attempting to log balance change')
    const { error: logError } = await supabase
      .from('balance_change_log')
      .insert({
        user_id: params.id,
        currency_code,
        amount_change: operation === 'set' ? balanceAfter - balanceBefore :
                      operation === 'add' ? amount : -amount,
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        change_type: 'admin_adjustment',
        admin_id: user.id,
        notes: notes || `Admin ${operation} operation`,
        tenant_id: adminProfile.tenant_id
      })

    if (logError) {
      console.error('Failed to log balance change:', logError)
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      balance: {
        currencyCode: currency_code,
        balanceBefore,
        balanceAfter,
        change: balanceAfter - balanceBefore
      }
    })

  } catch (error) {
    console.error('Error updating user balance:', error)
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
