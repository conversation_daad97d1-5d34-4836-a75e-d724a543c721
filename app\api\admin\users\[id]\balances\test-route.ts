import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../../lib/supabase/server'

// Simple test endpoint to debug balance updates
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('=== BALANCE UPDATE TEST START ===')
    
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    console.log('Auth user:', user ? user.id : 'No user', 'Auth error:', authError)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Request body:', body)
    
    // Get admin profile
    const { data: adminProfile, error: adminError } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()
    
    console.log('Admin profile:', adminProfile, 'Error:', adminError)
    
    if (!adminProfile || adminProfile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }
    
    // Check if currency exists
    const { data: currency, error: currencyError } = await supabase
      .from('currencies')
      .select('*')
      .eq('code', body.currency_code)
      .eq('tenant_id', adminProfile.tenant_id)
      .single()
    
    console.log('Currency check:', currency, 'Error:', currencyError)
    
    // Check current balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('user_currency_balances')
      .select('*')
      .eq('user_id', params.id)
      .eq('currency_code', body.currency_code)
      .eq('tenant_id', adminProfile.tenant_id)
      .single()
    
    console.log('Current balance:', currentBalance, 'Error:', balanceError)
    
    // Try simple insert/update
    if (currentBalance) {
      console.log('Updating existing balance')
      const { error: updateError } = await supabase
        .from('user_currency_balances')
        .update({ 
          balance: body.amount,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', params.id)
        .eq('currency_code', body.currency_code)
        .eq('tenant_id', adminProfile.tenant_id)
      
      console.log('Update result:', updateError)
      
      if (updateError) {
        return NextResponse.json({ 
          error: 'Update failed', 
          details: updateError.message 
        }, { status: 500 })
      }
    } else {
      console.log('Inserting new balance')
      const { error: insertError } = await supabase
        .from('user_currency_balances')
        .insert({
          user_id: params.id,
          currency_code: body.currency_code,
          balance: body.amount,
          tenant_id: adminProfile.tenant_id
        })
      
      console.log('Insert result:', insertError)
      
      if (insertError) {
        return NextResponse.json({ 
          error: 'Insert failed', 
          details: insertError.message 
        }, { status: 500 })
      }
    }
    
    console.log('=== BALANCE UPDATE TEST SUCCESS ===')
    
    return NextResponse.json({ 
      success: true,
      message: 'Balance updated successfully',
      debug: {
        user: user.id,
        admin: adminProfile,
        currency: currency,
        currentBalance: currentBalance
      }
    })
    
  } catch (error) {
    console.error('=== BALANCE UPDATE TEST ERROR ===', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
