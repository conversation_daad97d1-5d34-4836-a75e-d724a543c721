"use client"

import { useState, useEffect } from "react"
import { Search, Filter, Calendar, Package, Eye, Download, Copy, Check, Clock, CheckCircle, XCircle, Key, EyeOff } from "lucide-react"
import { useAuth } from "../contexts/AuthContext"
import { useData } from "../contexts/DataContext"
import { toast } from "sonner"
import { getCurrencySymbol } from "../utils/currency"

interface OrderWithDetails {
  id: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  custom_data: Record<string, any>
  created_at: string
  updated_at: string
  products: {
    id: string
    title: string
    slug: string
    coverImage: string
  }
  packages: {
    id: string
    name: string
    price: number
    image: string
  }
  digitalCodes?: {
    id: string
    key_encrypted: string
    used: boolean
    viewed_count: number
    last_viewed_at: string
  }[]
}

export default function OrdersPage() {
  const { authState } = useAuth()
  const { currencies, selectedCurrency, formatPrice } = useData()
  const authUser = authState.user

  // State management
  const [orders, setOrders] = useState<OrderWithDetails[]>([])
  const [filteredOrders, setFilteredOrders] = useState<OrderWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "pending" | "completed" | "failed">("all")
  const [dateFilter, setDateFilter] = useState<"all" | "today" | "week" | "month">("all")
  const [selectedOrder, setSelectedOrder] = useState<OrderWithDetails | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)
  const [visibleCodes, setVisibleCodes] = useState<Set<string>>(new Set())
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set())

  // Load orders from API
  const loadOrders = async () => {
    try {
      setLoading(true)
      console.log('Loading orders...')
      const response = await fetch('/api/orders')

      console.log('Orders response status:', response.status)

      if (!response.ok) {
        const errorData = await response.text()
        console.error('Orders API error:', response.status, errorData)
        throw new Error(`Failed to fetch orders: ${response.status} ${errorData}`)
      }

      const data = await response.json()
      console.log('Orders data:', data)
      setOrders(data.orders || [])
    } catch (error) {
      console.error('Error loading orders:', error)
      toast.error('فشل في تحميل الطلبات')
    } finally {
      setLoading(false)
    }
  }

  // Load digital codes for an order
  const loadDigitalCodes = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/digital-codes`)
      if (response.ok) {
        const data = await response.json()
        return data.codes || []
      }
    } catch (error) {
      console.error('Error loading digital codes:', error)
    }
    return []
  }

  // Filter orders based on search and filters
  useEffect(() => {
    let filtered = [...orders]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.products.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.packages.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Date filter
    if (dateFilter !== "all") {
      const now = new Date()
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.created_at)

        switch (dateFilter) {
          case "today":
            return orderDate.toDateString() === now.toDateString()
          case "week":
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            return orderDate >= weekAgo
          case "month":
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            return orderDate >= monthAgo
          default:
            return true
        }
      })
    }

    setFilteredOrders(filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()))
  }, [orders, searchTerm, statusFilter, dateFilter])

  // Load orders on component mount
  useEffect(() => {
    if (authUser) {
      loadOrders()
    }
  }, [authUser])

  // Authentication check - moved after hooks
  if (!authUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">يجب تسجيل الدخول أولاً</h1>
          <p className="text-gray-400">يرجى تسجيل الدخول للوصول إلى طلباتك</p>
        </div>
      </div>
    )
  }

  // Status styling helpers
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'failed':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Handle order details view
  const handleViewOrderDetails = async (order: OrderWithDetails) => {
    const codes = await loadDigitalCodes(order.id)
    setSelectedOrder({ ...order, digitalCodes: codes })
    setShowOrderDetails(true)
  }

  // Handle digital code visibility toggle
  const toggleCodeVisibility = (codeId: string) => {
    setVisibleCodes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(codeId)) {
        newSet.delete(codeId)
      } else {
        newSet.add(codeId)
      }
      return newSet
    })
  }

  // Handle code copying
  const copyCode = async (code: string, codeId: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedCodes(prev => new Set(prev).add(codeId))
      toast.success('تم نسخ الكود بنجاح')
      
      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopiedCodes(prev => {
          const newSet = new Set(prev)
          newSet.delete(codeId)
          return newSet
        })
      }, 2000)
    } catch (error) {
      toast.error('فشل في نسخ الكود')
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          طلباتي
        </h1>
        <p className="text-gray-400 text-lg">عرض وإدارة تاريخ طلباتك والأكواد الرقمية</p>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث في الطلبات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none"
            >
              <option value="all">جميع الحالات</option>
              <option value="completed">مكتملة</option>
              <option value="pending">قيد الانتظار</option>
              <option value="failed">فاشلة</option>
            </select>
          </div>

          {/* Date Filter */}
          <div className="relative">
            <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg pr-10 pl-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none"
            >
              <option value="all">جميع التواريخ</option>
              <option value="today">اليوم</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
            </select>
          </div>

          {/* Results Count */}
          <div className="flex items-center justify-center bg-gray-700/30 rounded-lg px-4 py-3">
            <Package className="w-5 h-5 text-purple-400 ml-2" />
            <span className="text-white font-semibold">{filteredOrders.length} طلب</span>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="mr-3 text-gray-400">جاري تحميل الطلبات...</span>
          </div>
        ) : filteredOrders.length > 0 ? (
          <div className="divide-y divide-gray-700/50">
            {filteredOrders.map((order) => {
              const orderCurrency = order.custom_data?.currency_code || 'USD'
              const orderAmount = order.custom_data?.amount_in_currency || order.amount
              const quantity = order.custom_data?.quantity || 1

              return (
                <div key={order.id} className="p-6 hover:bg-gray-700/20 transition-all duration-300">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    {/* Order Info */}
                    <div className="flex items-start space-x-4 space-x-reverse flex-1">
                      {/* Product Image */}
                      <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-700/50 flex-shrink-0">
                        <img
                          src={order.products.coverImage}
                          alt={order.products.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder-product.png'
                          }}
                        />
                      </div>

                      {/* Order Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-semibold text-white truncate">
                            {order.products.title}
                          </h3>
                          <div className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm border ${getStatusColor(order.status)}`}>
                            {getStatusIcon(order.status)}
                            <span>
                              {order.status === "completed" ? "مكتمل" :
                               order.status === "pending" ? "قيد الانتظار" : "فاشل"}
                            </span>
                          </div>
                        </div>

                        <p className="text-gray-300 mb-2">{order.packages.name}</p>

                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400">
                          <span>الكمية: {quantity}</span>
                          <span>#{order.id.slice(0, 8)}</span>
                          <span>{new Date(order.created_at).toLocaleDateString('ar-SA')}</span>
                        </div>
                      </div>
                    </div>

                    {/* Amount and Actions */}
                    <div className="flex items-center justify-between lg:justify-end gap-4">
                      <div className="text-left">
                        <div className="text-xl font-bold text-white">
                          {orderAmount.toFixed(2)} {getCurrencySymbol(orderCurrency)}
                        </div>
                        <div className="text-sm text-gray-400">
                          {order.custom_data?.payment_method === 'wallet' ? 'محفظة' : 'خارجي'}
                        </div>
                      </div>

                      <button
                        onClick={() => handleViewOrderDetails(order)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 flex items-center space-x-2 space-x-reverse"
                      >
                        <Eye className="w-4 h-4" />
                        <span>التفاصيل</span>
                      </button>
                    </div>
                  </div>

                  {/* Quick Custom Data Preview */}
                  {order.custom_data && Object.keys(order.custom_data).length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-700/50">
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(order.custom_data)
                          .filter(([key, value]) =>
                            !['quantity', 'currency_code', 'amount_in_currency', 'payment_method', 'email'].includes(key) &&
                            value !== null && value !== undefined && value !== ''
                          )
                          .slice(0, 3)
                          .map(([key, value]) => (
                            <span key={key} className="bg-gray-700/50 px-2 py-1 rounded text-xs text-gray-300">
                              {key}: {String(value)}
                            </span>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-semibold mb-2">لا توجد طلبات</h3>
            <p className="text-gray-400 mb-6">
              {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                ? 'لم يتم العثور على طلبات تطابق المعايير المحددة'
                : 'لم تقم بأي طلبات بعد'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && dateFilter === 'all' && (
              <button
                onClick={() => window.location.href = '/shop'}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300"
              >
                تصفح المنتجات
              </button>
            )}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">تفاصيل الطلب</h2>
              <button
                onClick={() => setShowOrderDetails(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* Order Summary */}
              <div className="bg-gray-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">معلومات الطلب</h3>
                  <div className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm border ${getStatusColor(selectedOrder.status)}`}>
                    {getStatusIcon(selectedOrder.status)}
                    <span>
                      {selectedOrder.status === "completed" ? "مكتمل" :
                       selectedOrder.status === "pending" ? "قيد الانتظار" : "فاشل"}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-gray-400 text-sm">رقم الطلب</p>
                    <p className="text-white font-mono">{selectedOrder.id}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">تاريخ الطلب</p>
                    <p className="text-white">{new Date(selectedOrder.created_at).toLocaleString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">المنتج</p>
                    <p className="text-white">{selectedOrder.products.title}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">الحزمة</p>
                    <p className="text-white">{selectedOrder.packages.name}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">المبلغ</p>
                    <p className="text-white font-bold">
                      {(selectedOrder.custom_data?.amount_in_currency || selectedOrder.amount).toFixed(2)} {getCurrencySymbol(selectedOrder.custom_data?.currency_code || 'USD')}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">طريقة الدفع</p>
                    <p className="text-white">{selectedOrder.custom_data?.payment_method === 'wallet' ? 'محفظة' : 'خارجي'}</p>
                  </div>
                </div>
              </div>

              {/* Custom Data */}
              {selectedOrder.custom_data && Object.keys(selectedOrder.custom_data).length > 0 && (
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-4">البيانات المخصصة</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(selectedOrder.custom_data)
                      .filter(([key, value]) =>
                        !['quantity', 'currency_code', 'amount_in_currency', 'payment_method'].includes(key) &&
                        value !== null && value !== undefined && value !== ''
                      )
                      .map(([key, value]) => (
                        <div key={key}>
                          <p className="text-gray-400 text-sm capitalize">{key.replace(/_/g, ' ')}</p>
                          <p className="text-white">{String(value)}</p>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Digital Codes */}
              {selectedOrder.digitalCodes && selectedOrder.digitalCodes.length > 0 && (
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <Key className="w-5 h-5 ml-2" />
                    الأكواد الرقمية
                  </h3>
                  <div className="space-y-3">
                    {selectedOrder.digitalCodes.map((code) => (
                      <div key={code.id} className="bg-gray-600/50 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-2">
                              <span className="text-white font-mono">
                                {visibleCodes.has(code.id) ? code.key_encrypted : '••••••••••••••••'}
                              </span>
                              <button
                                onClick={() => toggleCodeVisibility(code.id)}
                                className="text-gray-400 hover:text-white transition-colors"
                              >
                                {visibleCodes.has(code.id) ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-400">
                              <span>مرات المشاهدة: {code.viewed_count}</span>
                              {code.last_viewed_at && (
                                <span>آخر مشاهدة: {new Date(code.last_viewed_at).toLocaleDateString('ar-SA')}</span>
                              )}
                            </div>
                          </div>
                          <button
                            onClick={() => copyCode(code.key_encrypted, code.id)}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 flex items-center space-x-1 space-x-reverse"
                          >
                            {copiedCodes.has(code.id) ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                            <span>{copiedCodes.has(code.id) ? 'تم النسخ' : 'نسخ'}</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-end p-6 border-t border-gray-700 space-x-3 space-x-reverse">
              <button
                onClick={() => setShowOrderDetails(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
