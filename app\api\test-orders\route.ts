import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('Test Orders API - Starting...')
    const supabase = await createClient()
    
    console.log('Test Orders API - Getting user...')
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    console.log('Test Orders API - User:', user ? user.id : 'No user', 'Auth Error:', authError)
    
    if (!user) {
      return NextResponse.json({ 
        authenticated: false,
        error: 'No authenticated user',
        authError: authError?.message || null
      })
    }

    // Get user profile
    console.log('Test Orders API - Getting profile...')
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    console.log('Test Orders API - Profile:', profile, 'Error:', profileError)

    if (!profile) {
      return NextResponse.json({
        authenticated: true,
        user: { id: user.id, email: user.email },
        profile: null,
        profileError: profileError?.message || 'No profile found'
      })
    }

    // Try to get orders
    console.log('Test Orders API - Getting orders...')
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('*')
      .eq('user_id', user.id)
      .eq('tenant_id', profile.tenant_id)

    console.log('Test Orders API - Orders:', orders?.length || 0, 'Error:', ordersError)

    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      },
      profile: profile,
      orders: orders || [],
      ordersCount: orders?.length || 0,
      ordersError: ordersError?.message || null
    })

  } catch (error) {
    console.error('Test Orders API - Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
